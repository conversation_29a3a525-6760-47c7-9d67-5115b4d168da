# 本地开发环境脚本使用说明

## 概述

为了方便您在本地开发环境快速管理 solve-api 服务，我们提供了专门的开发脚本：

1. **r.sh** - 超简单一键重启（最常用）
2. **quick.sh** - 快速操作脚本
3. **restart.sh** - 本地开发重启脚本
4. **dev.sh** - 完整的开发环境管理工具

## 1. 超简单一键重启 (r.sh) ⭐ 推荐

最简单的重启方式，适合日常开发：

```bash
./r.sh                      # 一键重启，自动处理一切
```

**特点：**
- 🚀 一键操作，无需参数
- 🔥 自动强制停止所有相关进程
- 🎯 智能选择运行方式（二进制文件或源码）
- ✅ 自动检查启动状态
- 📋 显示访问地址和健康检查地址

## 2. 本地开发重启脚本 (restart.sh)

### 基本用法
```bash
./restart.sh                # 标准重启
```

### 开发选项
```bash
./restart.sh -f             # 强制重启（清理所有进程）
./restart.sh -b             # 重启前先编译
./restart.sh -c             # 重启后检查服务状态
```

### 组合使用
```bash
./restart.sh -b -c          # 编译 + 重启 + 检查
./restart.sh -f             # 强制重启（推荐）
```

## 3. 快速操作脚本 (quick.sh)

最常用的开发操作快捷方式：

### 重启相关
```bash
./quick.sh r                # 快速重启（最常用）
./quick.sh rf               # 强制重启
./quick.sh rb               # 编译并重启
./quick.sh rc               # 重启并检查
./quick.sh full             # 完整重启（编译+重启+检查）
```

### 查看相关
```bash
./quick.sh l                # 查看实时日志
./quick.sh lr               # 查看最近日志
./quick.sh s                # 查看服务状态
```

### 开发相关
```bash
./quick.sh b                # 编译项目
./quick.sh t                # 运行测试
./quick.sh d                # 开发模式运行 (go run)
./quick.sh c                # 清理进程
```

## 4. 完整开发环境管理工具 (dev.sh)

专业的开发环境管理，支持高级功能：

### 开发命令
```bash
./dev.sh run                # 直接运行 (go run main.go)
./dev.sh watch              # 监控文件变化自动重启
./dev.sh build              # 编译并运行
./dev.sh test               # 运行测试
./dev.sh clean              # 清理所有进程和文件
```

### 服务管理
```bash
./dev.sh start              # 启动服务
./dev.sh stop               # 停止服务
./dev.sh restart            # 重启服务
./dev.sh status             # 查看状态
./dev.sh logs               # 查看实时日志
```

### 实用工具
```bash
./dev.sh deps               # 安装/更新依赖
./dev.sh fmt                # 格式化代码
./dev.sh lint               # 代码检查
./dev.sh env                # 检查环境配置
```

## 常用开发场景

### 1. 最简单的重启（推荐）⭐
```bash
./r.sh                     # 一键重启，什么都不用想
```

### 2. 日常开发重启
```bash
./quick.sh r               # 快速重启
./quick.sh rb              # 编译后重启
```

### 3. 代码更新后完整重启
```bash
./quick.sh full            # 编译+重启+检查
# 或者
./restart.sh -b -c         # 编译+重启+检查
```

### 4. 服务异常时强制重启
```bash
./quick.sh rf              # 强制重启
# 或者
./restart.sh -f            # 强制重启
```

### 5. 查看服务状态和日志
```bash
./quick.sh s               # 查看状态
./quick.sh l               # 查看实时日志
./quick.sh lr              # 查看最近日志
```

### 6. 开发模式运行
```bash
./quick.sh d               # 开发模式 (go run)
./dev.sh run               # 直接运行
./dev.sh watch             # 监控文件变化
```

### 7. 测试和代码检查
```bash
./quick.sh t               # 运行测试
./dev.sh test              # 运行测试
./dev.sh fmt               # 格式化代码
./dev.sh lint              # 代码检查
```

## 功能特性

### 增强版重启脚本特性
- ✅ 彩色输出，清晰易读
- ✅ 多种重启模式
- ✅ 自动日志备份
- ✅ 完整健康检查
- ✅ 智能等待机制
- ✅ 强制重启选项

### 服务管理工具特性
- ✅ 一站式服务管理
- ✅ 自动编译部署
- ✅ 日志清理维护
- ✅ 版本备份功能
- ✅ 实时监控模式

### 快速操作脚本特性
- ✅ 极简命令格式
- ✅ 常用操作快捷方式
- ✅ 适合频繁使用

## 日志和备份

### 日志位置
- 主日志：`/tmp/solve-api.log`
- 重启备份日志：`./logs/restart_logs/`
- 版本备份：`./backups/`

### 自动清理
- 重启备份日志保留最近10个
- 可使用 `./service.sh clean` 手动清理

## 故障排除

### 1. 脚本没有执行权限
```bash
chmod +x *.sh
```

### 2. 服务启动失败
```bash
./quick.sh st              # 查看详细状态
./quick.sh l               # 查看错误日志
```

### 3. 端口被占用
```bash
netstat -tuln | grep :8080 # 检查端口占用
./quick.sh rf              # 强制重启
```

### 4. 进程僵死
```bash
./restart.sh -f            # 强制重启
```

## 推荐使用方式

1. **日常开发**：使用 `quick.sh` 进行快速操作
2. **生产环境**：使用 `restart.sh` 的完整选项
3. **维护管理**：使用 `service.sh` 的完整功能
4. **故障处理**：优先使用强制重启选项

## 注意事项

1. 所有脚本都会自动检查服务状态
2. 强制重启会跳过某些安全检查
3. 备份功能会自动创建必要的目录
4. 监控模式需要手动按 Ctrl+C 退出
5. 健康检查包括进程、端口和HTTP响应检查
