#!/bin/bash

# 本地开发环境管理脚本
# 专为本地开发设计，支持热重载、实时日志等

APP_NAME="solve-api"
APP_PORT=8080
LOG_FILE="/tmp/${APP_NAME}_dev.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

show_banner() {
    print_message $CYAN "=================================="
    print_message $CYAN "    本地开发环境管理工具"
    print_message $CYAN "=================================="
}

show_help() {
    show_banner
    echo ""
    print_message $BLUE "使用方法: $0 <命令>"
    echo ""
    print_message $GREEN "开发命令:"
    echo "  run         直接运行 (go run main.go)"
    echo "  watch       监控文件变化自动重启"
    echo "  build       编译并运行"
    echo "  test        运行测试"
    echo "  clean       清理所有进程和文件"
    echo ""
    print_message $YELLOW "服务管理:"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看状态"
    echo "  logs        查看实时日志"
    echo ""
    print_message $PURPLE "实用工具:"
    echo "  deps        安装/更新依赖"
    echo "  fmt         格式化代码"
    echo "  lint        代码检查"
    echo "  env         检查环境配置"
    echo ""
    print_message $CYAN "示例:"
    echo "  $0 run       # 直接运行开发服务器"
    echo "  $0 watch     # 监控文件变化"
    echo "  $0 test      # 运行测试"
}

# 检查Go环境
check_go_env() {
    if ! command -v go > /dev/null; then
        print_message $RED "❌ Go 未安装或不在 PATH 中"
        exit 1
    fi
    
    local go_version=$(go version | cut -d' ' -f3)
    print_message $GREEN "✅ Go 环境: $go_version"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查项目依赖..."
    
    if [ ! -f "go.mod" ]; then
        print_message $RED "❌ 未找到 go.mod 文件"
        exit 1
    fi
    
    # 检查是否需要下载依赖
    if ! go mod verify > /dev/null 2>&1; then
        print_message $YELLOW "⚠️  依赖需要更新，正在下载..."
        go mod download
    fi
    
    print_message $GREEN "✅ 依赖检查完成"
}

# 清理进程
clean_processes() {
    print_message $YELLOW "🧹 清理开发进程..."
    
    # 杀死相关进程
    pkill -f "$APP_NAME" 2>/dev/null || true
    pkill -f "go run main.go" 2>/dev/null || true
    pkill -f ":$APP_PORT" 2>/dev/null || true
    
    # 清理文件
    rm -f "/tmp/${APP_NAME}.pid" 2>/dev/null || true
    rm -f "$LOG_FILE" 2>/dev/null || true
    
    print_message $GREEN "✅ 清理完成"
}

# 直接运行
run_direct() {
    print_message $BLUE "🚀 直接运行开发服务器..."
    
    check_go_env
    check_dependencies
    clean_processes
    
    print_message $GREEN "启动服务器在 http://localhost:$APP_PORT"
    print_message $YELLOW "按 Ctrl+C 停止服务"
    echo "----------------------------------------"
    
    # 直接运行，输出到控制台
    go run main.go
}

# 监控文件变化（需要安装 fswatch 或 inotify-tools）
watch_files() {
    print_message $BLUE "👁️  启动文件监控模式..."
    
    # 检查监控工具
    if command -v fswatch > /dev/null; then
        WATCH_CMD="fswatch"
    elif command -v inotifywait > /dev/null; then
        WATCH_CMD="inotifywait"
    else
        print_message $RED "❌ 需要安装文件监控工具:"
        print_message $YELLOW "macOS: brew install fswatch"
        print_message $YELLOW "Linux: sudo apt-get install inotify-tools"
        exit 1
    fi
    
    print_message $GREEN "✅ 使用 $WATCH_CMD 监控文件变化"
    print_message $YELLOW "监控 .go 文件变化，自动重启服务"
    print_message $YELLOW "按 Ctrl+C 停止监控"
    echo "----------------------------------------"
    
    # 启动初始服务
    run_background
    
    # 监控文件变化
    if [ "$WATCH_CMD" = "fswatch" ]; then
        fswatch -o . --include='\.go$' | while read f; do
            print_message $BLUE "🔄 检测到文件变化，重启服务..."
            clean_processes
            sleep 1
            run_background
        done
    else
        while inotifywait -r -e modify,create,delete --include='\.go$' .; do
            print_message $BLUE "🔄 检测到文件变化，重启服务..."
            clean_processes
            sleep 1
            run_background
        done
    fi
}

# 后台运行
run_background() {
    check_go_env
    check_dependencies
    
    print_message $BLUE "🚀 后台启动服务..."
    nohup go run main.go > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "/tmp/${APP_NAME}.pid"
    
    sleep 2
    if ps -p $pid > /dev/null; then
        print_message $GREEN "✅ 服务启动成功 (PID: $pid)"
        print_message $BLUE "🌐 访问地址: http://localhost:$APP_PORT"
        print_message $BLUE "📋 日志文件: $LOG_FILE"
    else
        print_message $RED "❌ 服务启动失败"
        if [ -f "$LOG_FILE" ]; then
            print_message $YELLOW "错误日志:"
            tail -n 10 "$LOG_FILE"
        fi
    fi
}

# 编译并运行
build_and_run() {
    print_message $BLUE "🔨 编译项目..."
    
    check_go_env
    check_dependencies
    
    # 编译
    if go build -o $APP_NAME main.go; then
        print_message $GREEN "✅ 编译成功"
        
        clean_processes
        
        print_message $BLUE "🚀 启动编译后的服务..."
        nohup ./$APP_NAME > "$LOG_FILE" 2>&1 &
        local pid=$!
        echo $pid > "/tmp/${APP_NAME}.pid"
        
        sleep 2
        if ps -p $pid > /dev/null; then
            print_message $GREEN "✅ 服务启动成功 (PID: $pid)"
            print_message $BLUE "🌐 访问地址: http://localhost:$APP_PORT"
        else
            print_message $RED "❌ 服务启动失败"
        fi
    else
        print_message $RED "❌ 编译失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    print_message $BLUE "🧪 运行测试..."
    
    check_go_env
    
    # 运行测试
    if go test -v ./...; then
        print_message $GREEN "✅ 所有测试通过"
    else
        print_message $RED "❌ 测试失败"
        exit 1
    fi
}

# 查看状态
show_status() {
    print_message $BLUE "📊 开发环境状态..."
    
    # 检查进程
    if [ -f "/tmp/${APP_NAME}.pid" ]; then
        local pid=$(cat "/tmp/${APP_NAME}.pid")
        if ps -p $pid > /dev/null; then
            print_message $GREEN "✅ 服务运行中 (PID: $pid)"
        else
            print_message $RED "❌ 服务未运行"
            rm -f "/tmp/${APP_NAME}.pid"
        fi
    else
        print_message $RED "❌ 服务未运行"
    fi
    
    # 检查端口
    if netstat -tuln 2>/dev/null | grep ":$APP_PORT " > /dev/null || lsof -i :$APP_PORT > /dev/null 2>&1; then
        print_message $GREEN "✅ 端口 $APP_PORT 监听中"
    else
        print_message $RED "❌ 端口 $APP_PORT 未监听"
    fi
    
    # 检查日志文件
    if [ -f "$LOG_FILE" ]; then
        local log_size=$(wc -l < "$LOG_FILE")
        print_message $BLUE "📋 日志文件: $LOG_FILE ($log_size 行)"
    fi
}

# 查看实时日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_message $GREEN "📋 实时日志 (按 Ctrl+C 退出):"
        echo "----------------------------------------"
        tail -f "$LOG_FILE"
    else
        print_message $RED "❌ 日志文件不存在: $LOG_FILE"
    fi
}

# 安装/更新依赖
manage_deps() {
    print_message $BLUE "📦 管理项目依赖..."
    
    check_go_env
    
    print_message $YELLOW "更新依赖..."
    go mod tidy
    go mod download
    
    print_message $GREEN "✅ 依赖管理完成"
}

# 格式化代码
format_code() {
    print_message $BLUE "🎨 格式化代码..."
    
    check_go_env
    
    go fmt ./...
    
    print_message $GREEN "✅ 代码格式化完成"
}

# 代码检查
lint_code() {
    print_message $BLUE "🔍 代码检查..."
    
    check_go_env
    
    # 使用 go vet
    if go vet ./...; then
        print_message $GREEN "✅ 代码检查通过"
    else
        print_message $YELLOW "⚠️  发现代码问题"
    fi
    
    # 如果安装了 golint
    if command -v golint > /dev/null; then
        print_message $BLUE "运行 golint..."
        golint ./...
    fi
}

# 检查环境配置
check_env() {
    print_message $BLUE "🔧 检查环境配置..."
    
    check_go_env
    
    # 检查配置文件
    if [ -f ".env" ]; then
        print_message $GREEN "✅ 找到 .env 文件"
    else
        print_message $YELLOW "⚠️  未找到 .env 文件"
    fi
    
    # 检查数据库连接
    print_message $BLUE "数据库配置:"
    echo "  Host: ${DB_HOST:-localhost}"
    echo "  Port: ${DB_PORT:-3306}"
    echo "  User: ${DB_USER:-root}"
    echo "  Database: ${DB_NAME:-solve}"
    
    # 检查API配置
    if [ -n "$QWEN_API_KEY" ]; then
        print_message $GREEN "✅ QWEN API Key 已配置"
    else
        print_message $YELLOW "⚠️  QWEN API Key 未配置"
    fi
}

# 主函数
main() {
    case $1 in
        run)
            run_direct
            ;;
        watch)
            watch_files
            ;;
        build)
            build_and_run
            ;;
        test)
            run_tests
            ;;
        clean)
            clean_processes
            ;;
        start)
            run_background
            ;;
        stop)
            clean_processes
            ;;
        restart)
            clean_processes
            sleep 1
            run_background
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        deps)
            manage_deps
            ;;
        fmt)
            format_code
            ;;
        lint)
            lint_code
            ;;
        env)
            check_env
            ;;
        -h|--help|help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
