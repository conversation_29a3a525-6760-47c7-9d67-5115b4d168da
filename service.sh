#!/bin/bash

# 服务管理脚本 - 一键管理 solve-api 服务
# 使用方法: ./service.sh [start|stop|restart|status|logs|build|deploy]

APP_NAME="solve-api"
APP_PORT=8080

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示横幅
show_banner() {
    print_message $CYAN "=================================="
    print_message $CYAN "    Solve API 服务管理工具"
    print_message $CYAN "=================================="
}

# 显示使用帮助
show_help() {
    show_banner
    echo ""
    print_message $BLUE "使用方法: $0 <命令> [选项]"
    echo ""
    print_message $GREEN "可用命令:"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看服务状态"
    echo "  logs        查看实时日志"
    echo "  build       编译项目"
    echo "  deploy      部署到生产环境"
    echo "  clean       清理日志和临时文件"
    echo "  backup      备份当前版本"
    echo "  monitor     监控服务状态"
    echo ""
    print_message $YELLOW "重启选项:"
    echo "  restart -f  强制重启"
    echo "  restart -q  快速重启"
    echo "  restart -c  重启后健康检查"
    echo "  restart -b  重启前备份日志"
    echo ""
    print_message $PURPLE "示例:"
    echo "  $0 start                # 启动服务"
    echo "  $0 restart -f -c        # 强制重启并健康检查"
    echo "  $0 logs                 # 查看实时日志"
    echo "  $0 deploy               # 部署到生产环境"
}

# 启动服务
start_service() {
    print_message $GREEN "🚀 启动服务..."
    ./start.sh
}

# 停止服务
stop_service() {
    print_message $YELLOW "🛑 停止服务..."
    ./stop.sh
}

# 重启服务
restart_service() {
    print_message $BLUE "🔄 重启服务..."
    shift  # 移除 'restart' 参数
    ./restart.sh "$@"
}

# 查看状态
show_status() {
    print_message $CYAN "📊 查看服务状态..."
    ./status.sh
}

# 查看实时日志
show_logs() {
    local log_file="/tmp/${APP_NAME}.log"
    if [ -f "$log_file" ]; then
        print_message $GREEN "📋 实时日志 (按 Ctrl+C 退出):"
        echo "----------------------------------------"
        tail -f "$log_file"
    else
        print_message $RED "❌ 日志文件不存在: $log_file"
    fi
}

# 编译项目
build_project() {
    print_message $BLUE "🔨 编译项目..."
    if [ -f "./build.sh" ]; then
        ./build.sh
    else
        print_message $YELLOW "⚠️  未找到 build.sh，使用默认编译命令..."
        go build -o $APP_NAME main.go
        if [ $? -eq 0 ]; then
            print_message $GREEN "✅ 编译成功"
        else
            print_message $RED "❌ 编译失败"
            exit 1
        fi
    fi
}

# 部署到生产环境
deploy_service() {
    print_message $PURPLE "🚀 部署到生产环境..."
    
    # 先编译
    build_project
    
    # 检查部署包目录
    if [ -d "./deploy-package" ]; then
        print_message $BLUE "📦 复制文件到部署包..."
        cp $APP_NAME ./deploy-package/
        
        print_message $GREEN "✅ 部署包已更新"
        print_message $YELLOW "💡 提示: 可以将 deploy-package 目录上传到生产服务器"
    else
        print_message $YELLOW "⚠️  未找到 deploy-package 目录"
    fi
}

# 清理日志和临时文件
clean_files() {
    print_message $YELLOW "🧹 清理日志和临时文件..."
    
    # 清理临时日志
    if [ -f "/tmp/${APP_NAME}.log" ]; then
        > "/tmp/${APP_NAME}.log"
        print_message $GREEN "✅ 已清空主日志文件"
    fi
    
    # 清理旧的备份日志（保留最近10个）
    if [ -d "./logs/restart_logs" ]; then
        local backup_count=$(ls -1 ./logs/restart_logs/ | wc -l)
        if [ $backup_count -gt 10 ]; then
            print_message $BLUE "🗂️  清理旧的备份日志..."
            ls -t ./logs/restart_logs/ | tail -n +11 | xargs -I {} rm -f ./logs/restart_logs/{}
            print_message $GREEN "✅ 已清理旧的备份日志"
        fi
    fi
    
    # 清理编译缓存
    if [ -d "./tmp" ]; then
        rm -rf ./tmp
        print_message $GREEN "✅ 已清理临时目录"
    fi
    
    print_message $GREEN "🎉 清理完成"
}

# 备份当前版本
backup_version() {
    print_message $BLUE "💾 备份当前版本..."
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_dir="./backups/backup_$timestamp"
    
    mkdir -p "$backup_dir"
    
    # 备份二进制文件
    if [ -f "./$APP_NAME" ]; then
        cp "./$APP_NAME" "$backup_dir/"
        print_message $GREEN "✅ 已备份二进制文件"
    fi
    
    # 备份配置文件
    if [ -f "./.env" ]; then
        cp "./.env" "$backup_dir/"
    fi
    if [ -f "./.env.production" ]; then
        cp "./.env.production" "$backup_dir/"
    fi
    
    # 备份日志
    if [ -f "/tmp/${APP_NAME}.log" ]; then
        cp "/tmp/${APP_NAME}.log" "$backup_dir/app.log"
    fi
    
    print_message $GREEN "✅ 备份完成: $backup_dir"
}

# 监控服务状态
monitor_service() {
    print_message $CYAN "👁️  监控服务状态 (按 Ctrl+C 退出)..."
    echo "----------------------------------------"
    
    while true; do
        clear
        echo -e "${CYAN}=== $(date) ===${NC}"
        ./status.sh
        echo ""
        print_message $BLUE "下次检查: 30秒后..."
        sleep 30
    done
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    # 确保脚本有执行权限
    chmod +x *.sh 2>/dev/null
    
    case $1 in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service "$@"
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        build)
            build_project
            ;;
        deploy)
            deploy_service
            ;;
        clean)
            clean_files
            ;;
        backup)
            backup_version
            ;;
        monitor)
            monitor_service
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
