package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

// APILogger API专用日志记录器
type APILogger struct {
	logger *log.Logger
	file   *os.File
}

var (
	// 全局API日志记录器实例
	apiLogger *APILogger
)

// InitAPILogger 初始化API日志记录器
func InitAPILogger() error {
	// 创建logs目录
	logsDir := "logs"
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 创建API日志文件
	logFile := filepath.Join(logsDir, "api_details.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("创建API日志文件失败: %v", err)
	}

	// 创建日志记录器
	logger := log.New(file, "", log.LstdFlags|log.Lmicroseconds)

	apiLogger = &APILogger{
		logger: logger,
		file:   file,
	}

	// 记录初始化信息
	apiLogger.logger.Println("=== API详细日志记录器初始化成功 ===")
	
	return nil
}

// CloseAPILogger 关闭API日志记录器
func CloseAPILogger() {
	if apiLogger != nil && apiLogger.file != nil {
		apiLogger.file.Close()
	}
}

// LogQwenRawResponse 记录Qwen返回的原始数据
func LogQwenRawResponse(imageURL, rawResponse string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"==================== QWEN 原始响应 ====================\n" +
		"时间: %s\n" +
		"图片URL: %s\n" +
		"原始响应数据:\n%s\n" +
		"======================================================\n",
		timestamp, imageURL, rawResponse)
}

// LogDeepSeekRawResponse 记录DeepSeek返回的原始数据
func LogDeepSeekRawResponse(questionData, rawResponse string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"================== DEEPSEEK 原始响应 ==================\n" +
		"时间: %s\n" +
		"输入题目数据: %s\n" +
		"原始响应数据:\n%s\n" +
		"======================================================\n",
		timestamp, questionData, rawResponse)
}

// LogQwenParsedJSON 记录Qwen原始数据解析后的JSON
func LogQwenParsedJSON(rawResponse string, parsedData interface{}) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	
	// 将解析后的数据转换为格式化的JSON
	jsonData, err := json.MarshalIndent(parsedData, "", "  ")
	if err != nil {
		jsonData = []byte(fmt.Sprintf("JSON序列化失败: %v", err))
	}

	apiLogger.logger.Printf("\n" +
		"================= QWEN 解析后JSON ===================\n" +
		"时间: %s\n" +
		"原始响应: %s\n" +
		"解析后JSON:\n%s\n" +
		"======================================================\n",
		timestamp, rawResponse, string(jsonData))
}

// LogDeepSeekParsedJSON 记录DeepSeek原始数据解析后的JSON
func LogDeepSeekParsedJSON(rawResponse, answer, analysis string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	
	// 构建解析后的数据结构
	parsedData := map[string]interface{}{
		"answer":   answer,
		"analysis": analysis,
	}
	
	// 转换为格式化的JSON
	jsonData, err := json.MarshalIndent(parsedData, "", "  ")
	if err != nil {
		jsonData = []byte(fmt.Sprintf("JSON序列化失败: %v", err))
	}

	apiLogger.logger.Printf("\n" +
		"=============== DEEPSEEK 解析后JSON ==================\n" +
		"时间: %s\n" +
		"原始响应: %s\n" +
		"解析后JSON:\n%s\n" +
		"======================================================\n",
		timestamp, rawResponse, string(jsonData))
}

// LogAPICallSummary 记录API调用汇总信息
func LogAPICallSummary(imageURL, qwenResponse, deepseekResponse, finalAnswer, finalAnalysis string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"=================== API调用汇总 ======================\n" +
		"时间: %s\n" +
		"图片URL: %s\n" +
		"Qwen响应长度: %d 字符\n" +
		"DeepSeek响应长度: %d 字符\n" +
		"最终答案: %s\n" +
		"最终解析长度: %d 字符\n" +
		"======================================================\n",
		timestamp, imageURL, len(qwenResponse), len(deepseekResponse), finalAnswer, len(finalAnalysis))
}

// LogError 记录错误信息
func LogError(operation, errorMsg string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"===================== 错误日志 =======================\n" +
		"时间: %s\n" +
		"操作: %s\n" +
		"错误信息: %s\n" +
		"======================================================\n",
		timestamp, operation, errorMsg)
}

// LogDebug 记录调试信息
func LogDebug(operation, message string) {
	if apiLogger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	apiLogger.logger.Printf("\n" +
		"===================== 调试日志 =======================\n" +
		"时间: %s\n" +
		"操作: %s\n" +
		"信息: %s\n" +
		"======================================================\n",
		timestamp, operation, message)
}
