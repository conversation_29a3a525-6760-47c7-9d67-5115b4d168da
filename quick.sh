#!/bin/bash

# 本地开发快速操作脚本
# 使用方法: ./quick.sh [命令]

APP_NAME="solve-api"
APP_PORT=8080

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 启动开发服务
start_dev_service() {
    print_message $GREEN "🚀 启动开发服务..."

    # 本地开发环境直接使用 go run
    if [[ "$OSTYPE" == "darwin"* ]]; then
        print_message $BLUE "🔧 本地开发模式：使用 go run"
        nohup go run main.go > "/tmp/${APP_NAME}.log" 2>&1 &
    elif [ -f "./$APP_NAME" ] && file "./$APP_NAME" | grep -q "$(uname -m)"; then
        # Linux 环境且二进制文件架构匹配
        nohup ./$APP_NAME > "/tmp/${APP_NAME}.log" 2>&1 &
    else
        # 其他情况使用 go run
        print_message $BLUE "🔧 使用 go run 模式"
        nohup go run main.go > "/tmp/${APP_NAME}.log" 2>&1 &
    fi

    local pid=$!
    echo $pid > "/tmp/${APP_NAME}.pid"

    # 等待启动
    sleep 2

    # 检查是否启动成功
    if ps -p $pid > /dev/null 2>&1; then
        print_message $GREEN "✅ 服务启动成功 (PID: $pid)"
        print_message $BLUE "🌐 访问地址: http://localhost:$APP_PORT"
    else
        print_message $RED "❌ 服务启动失败"
        if [ -f "/tmp/${APP_NAME}.log" ]; then
            print_message $YELLOW "错误日志:"
            tail -n 5 "/tmp/${APP_NAME}.log"
        fi
    fi
}

# 快速重启
quick_restart() {
    print_message $BLUE "⚡ 快速重启..."

    # 停止服务
    clean_processes

    # 等待一下
    sleep 1

    # 启动服务
    start_dev_service
}

# 强制重启
force_restart() {
    print_message $YELLOW "🔥 强制重启..."
    clean_processes
    sleep 1
    start_dev_service
}

# 编译并重启
build_restart() {
    print_message $PURPLE "🔨 编译并重启..."
    build_project
    clean_processes
    sleep 1
    start_dev_service
}

# 重启并检查
restart_check() {
    print_message $BLUE "🔄 重启并检查..."
    clean_processes
    sleep 1
    start_dev_service
    sleep 2
    show_status
}

# 完整重启（编译+重启+检查）
full_restart() {
    print_message $PURPLE "🚀 完整重启（编译+重启+检查）..."
    build_project
    clean_processes
    sleep 1
    start_dev_service
    sleep 2
    show_status
}

# 查看日志
show_logs() {
    local log_file="/tmp/${APP_NAME}.log"
    if [ -f "$log_file" ]; then
        print_message $GREEN "📋 实时日志 (按 Ctrl+C 退出):"
        echo "----------------------------------------"
        tail -f "$log_file"
    else
        print_message $RED "❌ 日志文件不存在: $log_file"
        print_message $YELLOW "💡 尝试启动服务后再查看日志"
    fi
}

# 查看最近日志
show_recent_logs() {
    local log_file="/tmp/${APP_NAME}.log"
    if [ -f "$log_file" ]; then
        print_message $GREEN "📋 最近20行日志:"
        echo "----------------------------------------"
        tail -n 20 "$log_file"
    else
        print_message $RED "❌ 日志文件不存在"
    fi
}

# 查看状态
show_status() {
    print_message $BLUE "📊 服务状态检查..."

    # 检查进程（支持多种运行方式）
    local pid=""
    if [ -f "/tmp/${APP_NAME}.pid" ]; then
        local saved_pid=$(cat "/tmp/${APP_NAME}.pid")
        if ps -p $saved_pid > /dev/null 2>&1; then
            pid=$saved_pid
        fi
    fi

    # 如果PID文件中的进程不存在，尝试查找运行中的进程
    if [ -z "$pid" ]; then
        # 查找 go run main.go 进程
        pid=$(pgrep -f "go run main.go" | head -1)
        if [ -z "$pid" ]; then
            # 查找二进制文件进程
            pid=$(pgrep -f "$APP_NAME" | head -1)
        fi
    fi

    if [ -n "$pid" ]; then
        print_message $GREEN "✅ 进程运行中 (PID: $pid)"
    else
        print_message $RED "❌ 进程未运行"
        return
    fi

    # 检查端口
    if netstat -tuln 2>/dev/null | grep ":$APP_PORT " > /dev/null || lsof -i :$APP_PORT > /dev/null 2>&1; then
        print_message $GREEN "✅ 端口 $APP_PORT 监听中"
    else
        print_message $RED "❌ 端口 $APP_PORT 未监听"
    fi

    # 检查HTTP
    if command -v curl > /dev/null; then
        if curl -s --max-time 3 "http://localhost:$APP_PORT/health" > /dev/null 2>&1; then
            print_message $GREEN "✅ HTTP服务正常"
        else
            print_message $YELLOW "⚠️  HTTP服务异常"
        fi
    fi

    print_message $BLUE "🌐 访问地址: http://localhost:$APP_PORT"
}

# 编译项目
build_project() {
    print_message $BLUE "🔨 编译项目..."

    if [ -f "./build.sh" ]; then
        ./build.sh
    else
        go build -o $APP_NAME main.go
    fi

    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 编译成功"
    else
        print_message $RED "❌ 编译失败"
    fi
}

# 运行测试
run_tests() {
    print_message $BLUE "🧪 运行测试..."
    go test ./...
}

# 清理进程
clean_processes() {
    print_message $YELLOW "🧹 清理所有相关进程..."

    # 杀死 go run 进程
    pkill -f "go run main.go" 2>/dev/null || true

    # 杀死二进制文件进程
    pkill -f "$APP_NAME" 2>/dev/null || true

    # 杀死占用端口的进程
    local port_pid=$(lsof -ti :$APP_PORT 2>/dev/null)
    if [ -n "$port_pid" ]; then
        kill $port_pid 2>/dev/null || true
    fi

    # 清理PID文件
    rm -f "/tmp/${APP_NAME}.pid" 2>/dev/null || true

    # 等待进程完全停止
    sleep 1

    print_message $GREEN "✅ 清理完成"
}

# 开发模式运行（直接go run）
dev_run() {
    print_message $BLUE "🔧 开发模式运行..."
    clean_processes
    sleep 1
    print_message $GREEN "🚀 启动开发服务器..."
    go run main.go
}

case $1 in
    r|restart)
        quick_restart
        ;;
    rf|force)
        force_restart
        ;;
    rb|build)
        build_restart
        ;;
    rc|check)
        restart_check
        ;;
    rf|full)
        full_restart
        ;;
    l|logs)
        show_logs
        ;;
    lr|recent)
        show_recent_logs
        ;;
    s|st|status)
        show_status
        ;;
    b|build)
        build_project
        ;;
    t|test)
        run_tests
        ;;
    c|clean)
        clean_processes
        ;;
    d|dev)
        dev_run
        ;;
    *)
        echo "本地开发快速操作命令:"
        echo ""
        print_message $GREEN "重启相关:"
        echo "  r, restart  - 快速重启"
        echo "  rf, force   - 强制重启"
        echo "  rb, build   - 编译并重启"
        echo "  rc, check   - 重启并检查"
        echo "  full        - 完整重启（编译+重启+检查）"
        echo ""
        print_message $BLUE "查看相关:"
        echo "  l, logs     - 查看实时日志"
        echo "  lr, recent  - 查看最近日志"
        echo "  s, status   - 查看服务状态"
        echo ""
        print_message $PURPLE "开发相关:"
        echo "  b, build    - 编译项目"
        echo "  t, test     - 运行测试"
        echo "  d, dev      - 开发模式运行 (go run)"
        echo "  c, clean    - 清理进程"
        echo ""
        print_message $YELLOW "常用示例:"
        echo "  ./quick.sh r     # 最常用：快速重启"
        echo "  ./quick.sh rb    # 编译后重启"
        echo "  ./quick.sh l     # 查看日志"
        echo "  ./quick.sh s     # 查看状态"
        echo "  ./quick.sh d     # 开发模式"
        ;;
esac
