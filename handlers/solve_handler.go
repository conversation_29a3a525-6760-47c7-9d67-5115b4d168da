package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"solve-api/models"
	"solve-api/services"
	"solve-api/utils"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SolveHandler 解题处理器
type SolveHandler struct {
	qwenService  *services.QwenService
	hybridCache  *services.HybridCache
}

// NewSolveHandler 创建解题处理器实例
func NewSolveHandler() *SolveHandler {
	return &SolveHandler{
		qwenService: services.NewQwenService(),
		hybridCache: services.NewHybridCache(),
	}
}

// Solve 解题接口
func (h *SolveHandler) Solve(c *gin.Context) {
	var req models.SolveRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "请求参数错误",
			Error:   err.<PERSON>rror(),
		})
		return
	}

	// 验证业务逻辑类型
	if !isValidQuType(req.QuType) {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "无效的业务逻辑类型",
			Error:   "qu_type 当前只支持 1",
		})
		return
	}

	// 验证图片 URL
	if !isValidURL(req.ImgURL) {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "无效的图片 URL",
			Error:   "img_url 必须是有效的 HTTP/HTTPS URL",
		})
		return
	}

	// 检查图片 URL 是否可访问（添加超时控制）
	if !isURLAccessibleWithTimeout(req.ImgURL, 5*time.Second) {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    400,
			Message: "图片 URL 无法访问",
			Error:   "请确保图片 URL 可以正常访问",
		})
		return
	}

	// 第一步：调用 Qwen-VL 提取题目内容
	qwenRawResult, err := h.qwenService.AnalyzeImageRaw(req.ImgURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "图片分析失败",
			Error:   err.Error(),
		})
		return
	}

	// 🔍 打印Qwen返回的原始数据
	fmt.Println("\n" + strings.Repeat("=", 100))
	fmt.Println("🔍 QWEN 原始数据 (解析前)")
	fmt.Println(strings.Repeat("=", 100))
	fmt.Printf("图片URL: %s\n", req.ImgURL)
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05.000"))
	fmt.Printf("原始数据长度: %d 字符\n", len(qwenRawResult))
	fmt.Println("原始数据内容:")
	fmt.Println(qwenRawResult)
	fmt.Println(strings.Repeat("=", 100))
	fmt.Println()

	// 第二步：解析Qwen结果获取题目信息
	questionData, err := services.ParseQwenExtraction(qwenRawResult)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    500,
			Message: "解析Qwen题目提取失败",
			Error:   err.Error(),
		})
		return
	}

	// 第三步：检查混合缓存（Redis + MySQL）
	var cacheHitInfo *models.CacheHitInfo

	// 尝试从缓存获取完整数据
	cachedData, hitInfo, err := h.hybridCache.Get(questionData)
	cacheHitInfo = hitInfo

	if err == nil && cachedData != nil {
		// 缓存命中，使用缓存的完整数据
		questionData = cachedData
		log.Printf("🎯 缓存命中 [%s]: %s", hitInfo.Type, hitInfo.Source)
	} else {
		// 缓存未命中，调用DeepSeek分析
		log.Printf("🔍 缓存未命中，调用DeepSeek分析")

		// 将结构化的题目数据传给DeepSeek，而不是Qwen原始响应
		deepseekResult, err := services.AnalyzeQuestionWithDeepSeek(formatQuestionForDeepSeek(questionData))
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    500,
				Message: "DeepSeek 分析失败",
				Error:   err.Error(),
			})
			return
		}

		// 解析DeepSeek结果
		answerLetters, analysis, err := services.ParseDeepSeekResponse(deepseekResult)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    500,
				Message: "解析DeepSeek响应失败",
				Error:   err.Error(),
			})
			return
		}

		// 根据题型格式化最终答案
		formattedAnswer := formatAnswerByQuestionType(questionData.Type, answerLetters, questionData.Options)

		// 设置答案和解析
		questionData.Answer = formattedAnswer
		questionData.Analysis = analysis

		// 异步设置到混合缓存（Redis + MySQL）
		go func() {
			if err := h.hybridCache.Set(questionData); err != nil {
				log.Printf("❌ 设置混合缓存失败: %v", err)
			}
		}()
	}

	// 验证返回的数据格式（只在有答案时验证）
	if questionData.Answer != "" {
		if err := validateQuestionData(questionData); err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    500,
				Message: "AI 返回数据格式错误",
				Error:   err.Error(),
			})
			return
		}
	} else {
		// 基本验证（不包括答案和解析）
		if questionData.Type == "" {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    500,
				Message: "AI 返回数据格式错误",
				Error:   "题目类型不能为空",
			})
			return
		}
		if questionData.Question == "" {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    500,
				Message: "AI 返回数据格式错误",
				Error:   "题目内容不能为空",
			})
			return
		}
		if len(questionData.Options) == 0 {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Code:    500,
				Message: "AI 返回数据格式错误",
				Error:   "选项不能为空",
			})
			return
		}
	}

	// 记录API调用汇总日志
	var qwenResponseSummary, deepseekResponseSummary string
	if cacheHitInfo == nil || cacheHitInfo.Type == models.CacheTypeMiss {
		// 只有在实际调用API时才记录汇总
		qwenResponseSummary = qwenRawResult
		if len(qwenResponseSummary) > 100 {
			qwenResponseSummary = qwenResponseSummary[:100] + "..."
		}
		// deepseekResponseSummary 在实际调用时会被设置
	}
	utils.LogAPICallSummary(req.ImgURL, qwenResponseSummary, deepseekResponseSummary,
		questionData.Answer, questionData.Analysis)

	// 返回成功响应
	message := "解析成功"
	if cacheHitInfo != nil && cacheHitInfo.Type != models.CacheTypeMiss {
		switch cacheHitInfo.Type {
		case models.CacheTypeRedis:
			message = "解析成功(Redis缓存命中)"
		case models.CacheTypeMySQL:
			message = "解析成功(MySQL缓存命中)"
		}
	}

	c.JSON(http.StatusOK, models.SolveResponse{
		Code:    200,
		Message: message,
		Data:    questionData,
	})
}

// isValidQuType 验证业务逻辑类型
func isValidQuType(quType string) bool {
	validTypes := []string{"1"} // 当前只支持业务逻辑类型1，未来可扩展2,3,4,5,6...
	for _, t := range validTypes {
		if t == quType {
			return true
		}
	}
	return false
}

// isValidURL 验证 URL 格式
func isValidURL(urlStr string) bool {
	u, err := url.Parse(urlStr)
	return err == nil && (u.Scheme == "http" || u.Scheme == "https") && u.Host != ""
}

// isURLAccessibleWithTimeout 检查 URL 是否可访问（带超时控制）
func isURLAccessibleWithTimeout(urlStr string, timeout time.Duration) bool {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "HEAD", urlStr, nil)
	if err != nil {
		return false
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// validateQuestionData 验证题目数据格式
func validateQuestionData(data *models.QuestionData) error {
	if data.Type == "" {
		return fmt.Errorf("题目类型不能为空")
	}
	if data.Question == "" {
		return fmt.Errorf("题目内容不能为空")
	}
	if len(data.Options) == 0 {
		return fmt.Errorf("选项不能为空")
	}
	if data.Answer == "" {
		return fmt.Errorf("答案不能为空")
	}
	if data.Analysis == "" {
		return fmt.Errorf("解析不能为空")
	}
	return nil
}

// formatAnswerByQuestionType 根据题型格式化最终答案
func formatAnswerByQuestionType(questionType, answerLetters string, options map[string]string) string {
	if answerLetters == "" {
		return ""
	}

	switch questionType {
	case "判断题":
		return formatJudgmentAnswer(answerLetters)
	case "单选题":
		return formatSingleChoiceAnswer(answerLetters, options)
	case "多选题":
		return formatMultipleChoiceAnswer(answerLetters, options)
	default:
		// 默认返回原始答案字母
		return answerLetters
	}
}

// formatJudgmentAnswer 格式化判断题答案
func formatJudgmentAnswer(answerLetters string) string {
	switch answerLetters {
	case "Y":
		return "[Y：正确]"
	case "N":
		return "[N：错误]"
	default:
		return answerLetters
	}
}

// formatSingleChoiceAnswer 格式化单选题答案
func formatSingleChoiceAnswer(answerLetters string, options map[string]string) string {
	if len(answerLetters) != 1 {
		return answerLetters
	}

	letter := answerLetters
	if content, exists := options[letter]; exists {
		return fmt.Sprintf("[%s：%s]", letter, content)
	}

	return answerLetters
}

// formatMultipleChoiceAnswer 格式化多选题答案
func formatMultipleChoiceAnswer(answerLetters string, options map[string]string) string {
	if answerLetters == "" {
		return ""
	}

	var answerParts []string

	// 遍历答案字母
	for _, letter := range answerLetters {
		letterStr := string(letter)
		if content, exists := options[letterStr]; exists {
			answerParts = append(answerParts, fmt.Sprintf("%s：%s", letterStr, content))
		}
	}

	if len(answerParts) > 0 {
		return fmt.Sprintf("[%s]", strings.Join(answerParts, "；"))
	}

	return answerLetters
}

// formatQuestionForDeepSeek 将结构化题目数据格式化为适合DeepSeek分析的文本
func formatQuestionForDeepSeek(questionData *models.QuestionData) string {
	var builder strings.Builder

	// 题目类型
	builder.WriteString(fmt.Sprintf("题目类型：%s\n", questionData.Type))

	// 题目内容
	builder.WriteString(fmt.Sprintf("问题：%s\n", questionData.Question))

	// 选项内容（按字母顺序排列）
	var optionKeys []string
	for key := range questionData.Options {
		optionKeys = append(optionKeys, key)
	}

	// 对选项键进行排序
	sort.Strings(optionKeys)

	// 添加选项
	for _, key := range optionKeys {
		if content, exists := questionData.Options[key]; exists {
			builder.WriteString(fmt.Sprintf("选项%s：%s\n", key, content))
		}
	}

	return strings.TrimSpace(builder.String())
}
