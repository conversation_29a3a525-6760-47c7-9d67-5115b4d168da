#!/bin/bash

# 本地开发环境重启脚本
# 简单快速的重启方式，适合开发调试

APP_NAME="solve-api"
APP_PORT=8080

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示使用帮助
show_help() {
    echo "本地开发环境重启脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -f, --force    强制重启（杀死所有相关进程）"
    echo "  -b, --build    重启前先编译"
    echo "  -c, --check    重启后检查服务状态"
    echo ""
    echo "示例:"
    echo "  $0             # 标准重启"
    echo "  $0 -f          # 强制重启"
    echo "  $0 -b          # 编译后重启"
    echo "  $0 -b -c       # 编译、重启、检查"
}

# 强制停止所有相关进程
force_stop() {
    print_message $YELLOW "🔥 强制停止所有相关进程..."

    # 查找并杀死所有相关进程
    pkill -f "$APP_NAME" 2>/dev/null || true
    pkill -f "go run main.go" 2>/dev/null || true
    pkill -f ":$APP_PORT" 2>/dev/null || true

    # 等待进程完全停止
    sleep 1

    # 清理可能的PID文件
    rm -f "/tmp/${APP_NAME}.pid" 2>/dev/null || true

    print_message $GREEN "✅ 进程清理完成"
}

# 普通停止
normal_stop() {
    print_message $BLUE "🛑 停止服务..."

    # 如果有stop.sh脚本，使用它
    if [ -f "./stop.sh" ]; then
        ./stop.sh 2>/dev/null || true
    else
        # 否则查找进程并停止
        local pid=$(pgrep -f "$APP_NAME" 2>/dev/null | head -1)
        if [ -n "$pid" ]; then
            kill $pid 2>/dev/null || true
            sleep 1
        fi
    fi
}

# 编译项目
build_project() {
    print_message $BLUE "🔨 编译项目..."

    if [ -f "./build.sh" ]; then
        ./build.sh
    else
        go build -o $APP_NAME main.go
    fi

    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 编译成功"
    else
        print_message $RED "❌ 编译失败"
        exit 1
    fi
}

# 启动服务
start_service() {
    print_message $GREEN "🚀 启动服务..."

    # 启动服务
    if [ -f "./start.sh" ]; then
        ./start.sh
    else
        # 本地开发环境直接使用 go run
        if [[ "$OSTYPE" == "darwin"* ]]; then
            print_message $BLUE "🔧 本地开发模式：使用 go run"
            nohup go run main.go > "/tmp/${APP_NAME}.log" 2>&1 &
        elif [ -f "./$APP_NAME" ] && file "./$APP_NAME" | grep -q "$(uname -m)"; then
            # Linux 环境且二进制文件架构匹配
            nohup ./$APP_NAME > "/tmp/${APP_NAME}.log" 2>&1 &
        else
            # 其他情况使用 go run
            print_message $BLUE "🔧 使用 go run 模式"
            nohup go run main.go > "/tmp/${APP_NAME}.log" 2>&1 &
        fi

        local pid=$!
        echo $pid > "/tmp/${APP_NAME}.pid"
        print_message $GREEN "✅ 服务已启动 (PID: $pid)"
    fi
}

# 检查服务状态
check_status() {
    print_message $BLUE "🔍 检查服务状态..."

    sleep 2  # 等待服务启动

    # 检查进程
    if pgrep -f "$APP_NAME" > /dev/null; then
        print_message $GREEN "✅ 进程运行正常"
    else
        print_message $RED "❌ 进程未运行"
        return 1
    fi

    # 检查端口
    if netstat -tuln 2>/dev/null | grep ":$APP_PORT " > /dev/null; then
        print_message $GREEN "✅ 端口 $APP_PORT 监听正常"
    elif lsof -i :$APP_PORT > /dev/null 2>&1; then
        print_message $GREEN "✅ 端口 $APP_PORT 监听正常"
    else
        print_message $YELLOW "⚠️  端口 $APP_PORT 可能未监听"
    fi

    # 检查HTTP响应
    if command -v curl > /dev/null; then
        if curl -s --max-time 3 "http://localhost:$APP_PORT/health" > /dev/null 2>&1; then
            print_message $GREEN "✅ HTTP健康检查通过"
        else
            print_message $YELLOW "⚠️  HTTP健康检查失败（服务可能还在启动）"
        fi
    fi

    # 显示访问地址
    print_message $BLUE "🌐 服务地址: http://localhost:$APP_PORT"
    print_message $BLUE "🏥 健康检查: http://localhost:$APP_PORT/health"
}

# 解析命令行参数
FORCE_RESTART=false
BUILD_FIRST=false
CHECK_AFTER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE_RESTART=true
            shift
            ;;
        -b|--build)
            BUILD_FIRST=true
            shift
            ;;
        -c|--check)
            CHECK_AFTER=true
            shift
            ;;
        *)
            print_message $RED "❌ 未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主重启逻辑
main() {
    print_message $BLUE "🔄 本地开发环境重启 $APP_NAME..."

    # 编译（如果需要）
    if [ "$BUILD_FIRST" = true ]; then
        build_project
    fi

    # 停止服务
    if [ "$FORCE_RESTART" = true ]; then
        force_stop
    else
        normal_stop
    fi

    # 等待一下
    sleep 1

    # 启动服务
    start_service

    # 检查状态（如果需要）
    if [ "$CHECK_AFTER" = true ]; then
        check_status
    else
        print_message $GREEN "🎉 重启完成！"
        print_message $BLUE "💡 使用 './restart.sh -c' 可以检查服务状态"
    fi
}

# 执行主函数
main
